package com.datayes.quarkus.rest

import com.datayes.llm.LlmPromptBuilder
import com.datayes.model.ExtractionResponse
import com.datayes.quarkus.client.ExtractContentRequest
import com.datayes.quarkus.client.RemotePdfContentClient
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.context.control.ActivateRequestContext
import org.eclipse.microprofile.rest.client.inject.RestClient

/**
 * 示例提示构建服务 (Example Prompt Building Service)
 * 用于根据表ID构建LLM示例会话
 */
@ApplicationScoped
class ExamplePromptService(@RestClient private val pdfContentClient: RemotePdfContentClient) {

    /**
     * 根据表ID构建示例会话
     *
     * @param taskId 表ID
     * @return 示例会话内容，如果没有找到示例数据则返回null
     */
    @ActivateRequestContext
    fun buildExamplePrompt(taskId: Long): String? {
        // 1. 查找示例数据 (Find example data)
        val exampleData = ExtractionResponse.findByTaskId(taskId)
        if (exampleData.isEmpty()) {
            return null
        }

        // 2. 按reportAddress分组 (Group by reportAddress)
        val groupedData = exampleData.groupBy { it.reportAddress }

        // 3. 选择第一组数据 (Select first group of data)
        val (reportAddress, dataList) = groupedData.entries.firstOrNull() ?: return null

        // 4. 读取PDF内容 (Read PDF content)
        val pdfUrl = reportAddress?.takeIf { it.isNotBlank() }?.let { "https://bigdata-s3.datayes.com$it" }
            ?: return null

        val pdfContent = try {
            val lines = pdfContentClient.extractContent(ExtractContentRequest(pdfUrl))
            lines.joinToString("\n")
        } catch (e: Exception) {
            Log.error("Failed to extract PDF content", e)
            return null
        }

        // 5. 构建示例部分 (Build example section)
        // 使用extractionFields替代relationFields (Use extractionFields instead of relationFields)
        val extractionFieldsList = dataList.mapNotNull { it.extractionFields }
        return LlmPromptBuilder.buildExamplesSection(pdfContent, extractionFieldsList)
    }
}